import { <PERSON>MService, LLMConfig, LLMProvider } from './llmService';

export interface ChatMessage {
  id: string;
  text: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  isTyping?: boolean;
}

export interface ChatbotConfig {
  llmConfig?: LLMConfig;
  useLLM: boolean;
}

export class ChatbotService {
  private llmService?: LLMService;
  private useLLM: boolean;
  private conversationHistory: ChatMessage[] = [];

  constructor(config: ChatbotConfig) {
    this.useLLM = config.useLLM;
    if (config.llmConfig && config.useLLM) {
      this.llmService = new LLMService(config.llmConfig);
    }
  }

  async generateResponse(userMessage: string): Promise<string> {
    // Add user message to conversation history
    this.conversationHistory.push({
      id: Date.now().toString(),
      text: userMessage,
      sender: 'user',
      timestamp: new Date()
    });

    if (this.useLLM && this.llmService) {
      return await this.generateLLMResponse(userMessage);
    } else {
      return this.generateRuleBasedResponse(userMessage);
    }
  }

  private async generateLLMResponse(userMessage: string): Promise<string> {
    try {
      const systemPrompt = this.getChatbotSystemPrompt();
      const contextualPrompt = this.buildContextualPrompt(userMessage);
      
      const response = await this.llmService!.callLLMAPI(systemPrompt, contextualPrompt);
      
      // Add bot response to conversation history
      this.conversationHistory.push({
        id: (Date.now() + 1).toString(),
        text: response,
        sender: 'bot',
        timestamp: new Date()
      });

      return response;
    } catch (error) {
      console.error('Error generating LLM response:', error);
      return this.generateRuleBasedResponse(userMessage);
    }
  }

  private getChatbotSystemPrompt(): string {
    return `You are a professional Course Design Assistant specializing in Indian higher education. Your expertise includes:

**Core Competencies:**
- AICTE (All India Council for Technical Education) guidelines and compliance
- UGC (University Grants Commission) standards and frameworks
- NEP 2020 (National Education Policy) implementation
- Bloom's Taxonomy and learning outcome design
- Indian university credit systems and assessment patterns
- Curriculum design best practices
- Industry-academia alignment
- Academic quality assurance

**Communication Style:**
- Professional and knowledgeable
- Provide specific, actionable advice
- Reference official guidelines when relevant
- Use clear, academic language
- Be helpful and supportive
- Acknowledge limitations when appropriate

**Key Areas of Assistance:**
1. Course structure and credit allocation
2. Learning objectives and outcomes design
3. Assessment pattern recommendations
4. AICTE compliance requirements
5. Unit planning and topic organization
6. Textbook and resource selection
7. Industry relevance and skill development
8. Academic calendar and scheduling

**Response Guidelines:**
- Keep responses concise but comprehensive
- Provide practical examples when helpful
- Reference specific AICTE/UGC guidelines when applicable
- Suggest next steps or follow-up actions
- Maintain professional academic tone
- If unsure about specific regulations, recommend consulting official sources

You are here to help educators create high-quality, compliant, and effective course curricula for Indian universities.`;
  }

  private buildContextualPrompt(userMessage: string): string {
    // Get recent conversation context (last 6 messages)
    const recentHistory = this.conversationHistory.slice(-6);
    let contextPrompt = '';

    if (recentHistory.length > 0) {
      contextPrompt = '\n**Conversation Context:**\n';
      recentHistory.forEach(msg => {
        contextPrompt += `${msg.sender === 'user' ? 'User' : 'Assistant'}: ${msg.text}\n`;
      });
      contextPrompt += '\n';
    }

    return `${contextPrompt}**Current Question:** ${userMessage}

Please provide a helpful, professional response based on your expertise in Indian higher education and course design. If the question is outside your domain, politely redirect to course design topics.`;
  }

  private generateRuleBasedResponse(userMessage: string): string {
    const lowerMessage = userMessage.toLowerCase();
    
    // Enhanced rule-based responses with more comprehensive coverage
    const responses = {
      // AICTE Guidelines
      'aicte': 'AICTE guidelines ensure quality technical education in India. Key requirements include: minimum 75% attendance, continuous assessment (40% internal + 60% external), qualified faculty with PhD/industry experience, adequate infrastructure, and industry-relevant curriculum. For specific compliance details, refer to the latest AICTE handbook.',
      
      'compliance': 'AICTE compliance involves: 1) Faculty qualifications (PhD/M.Tech with industry experience), 2) Infrastructure standards (labs, library, classrooms), 3) Curriculum approval, 4) Assessment patterns (40:60 internal:external), 5) Student-faculty ratio, 6) Industry partnerships. Regular audits ensure adherence.',
      
      // Course Structure
      'credits': 'Indian universities typically follow: Theory courses (3-4 credits), Practical/Lab courses (1-2 credits), Project work (2-6 credits). Total credits per semester: 20-24 for undergraduate, 16-20 for postgraduate. Contact hours: 1 credit = 15-16 contact hours per semester.',
      
      'contact hours': 'Contact hour distribution: Lecture (L), Tutorial (T), Practical (P) format. Example: L-T-P = 3-1-2 means 3 hours lecture, 1 hour tutorial, 2 hours practical per week. Total contact hours = (L+T+P) × weeks in semester.',
      
      // Learning Outcomes
      'learning outcomes': 'Learning outcomes should be SMART (Specific, Measurable, Achievable, Relevant, Time-bound) and mapped to Bloom\'s taxonomy levels: Remember, Understand, Apply, Analyze, Evaluate, Create. Each course should have 4-6 clear outcomes linked to program outcomes.',
      
      'bloom\'s taxonomy': 'Bloom\'s levels for course outcomes: 1) Remember (recall facts), 2) Understand (explain concepts), 3) Apply (use knowledge), 4) Analyze (break down problems), 5) Evaluate (make judgments), 6) Create (produce new work). Distribute outcomes across these levels.',
      
      // Assessment
      'assessment': 'Indian university assessment pattern: Internal Assessment (40%) includes assignments, quizzes, mid-term exams, attendance. End Semester Exam (60%) tests comprehensive understanding. Practical courses may have 50% internal, 50% external evaluation.',
      
      'evaluation': 'Evaluation methods: Formative (ongoing feedback) and Summative (final grades). Use diverse methods: written exams, practical tests, projects, presentations, assignments. Ensure alignment with learning outcomes and fair distribution of marks.',
      
      // Curriculum Design
      'curriculum': 'Curriculum design principles: 1) Align with program objectives, 2) Progressive difficulty, 3) Theory-practice balance, 4) Industry relevance, 5) Interdisciplinary connections, 6) Skill development focus, 7) Regular updates based on feedback.',
      
      'units': 'Course units should be: 1) Logically sequenced, 2) Balanced in content load, 3) 8-12 contact hours each, 4) Include 6-8 detailed topics, 5) Have specific learning outcomes, 6) Connect to overall course objectives.',
      
      // Resources
      'textbooks': 'Textbook selection criteria: 1) Recent publication (within 5 years), 2) Reputed authors and publishers, 3) Comprehensive coverage, 4) Suitable for Indian context, 5) Available in libraries, 6) Supplemented with reference books and online resources.',
      
      'resources': 'Course resources should include: Primary textbooks (2-3), Reference books (4-5), Online resources (MOOCs, documentation), Research papers, Industry reports, Case studies. Ensure accessibility and relevance to Indian students.',
      
      // NEP 2020
      'nep 2020': 'NEP 2020 emphasizes: Multidisciplinary education, Choice-based credit system, Multiple entry-exit points, Skill development, Research orientation, Technology integration, Continuous assessment, Holistic development. Implement gradually with institutional support.',
      
      // Industry Alignment
      'industry': 'Industry alignment strategies: 1) Industry expert guest lectures, 2) Live projects and internships, 3) Current technology integration, 4) Skill-based learning, 5) Industry mentorship, 6) Regular curriculum review with industry feedback.',
      
      // Quality Assurance
      'quality': 'Quality assurance involves: 1) Regular curriculum review, 2) Faculty development programs, 3) Student feedback analysis, 4) Industry input, 5) Outcome-based education, 6) Continuous improvement, 7) Accreditation compliance (NBA/NAAC).'
    };

    // Check for keyword matches
    for (const [keyword, response] of Object.entries(responses)) {
      if (lowerMessage.includes(keyword)) {
        return response;
      }
    }

    // Resource requests
    if (lowerMessage.includes('website') || lowerMessage.includes('link') || lowerMessage.includes('resource')) {
      return this.getWebsiteRecommendations(lowerMessage);
    }

    if (lowerMessage.includes('textbook') || lowerMessage.includes('book') || lowerMessage.includes('reference')) {
      return this.getTextbookRecommendations(lowerMessage);
    }

    // Subject-specific responses
    if (lowerMessage.includes('machine learning') || lowerMessage.includes('ml')) {
      return this.getMachineLearningResources();
    }

    if (lowerMessage.includes('data structure') || lowerMessage.includes('dsa')) {
      return this.getDataStructuresResources();
    }

    // Greeting responses
    if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {
      return 'Hello! I\'m your Course Design Assistant. I can help you with AICTE guidelines, curriculum design, learning outcomes, assessment patterns, website links, textbook recommendations, and other aspects of Indian higher education. What would you like to know?';
    }

    // Help responses
    if (lowerMessage.includes('help') || lowerMessage.includes('what can you do')) {
      return `I can assist you with:

📚 **Course Design**: Structure, credits, contact hours, unit planning
🎯 **Learning Outcomes**: Bloom's taxonomy, measurable objectives
📊 **Assessment**: Internal/external patterns, evaluation methods
📋 **AICTE Compliance**: Guidelines, requirements, standards
📖 **Resources**: Textbook selection, reference materials, website links
🏭 **Industry Alignment**: Current trends, skill development
🔍 **Quality Assurance**: Best practices, continuous improvement
🌐 **Online Resources**: Website recommendations for specific subjects
📚 **Textbook Recommendations**: Subject-specific book suggestions

What specific topic would you like to explore?`;
    }

    // Default response
    return `That's an interesting question about course design! While I have knowledge about curriculum development, AICTE guidelines, and Indian higher education standards, I'd recommend consulting the latest official guidelines for specific technical details. 

Is there anything specific about course structure, learning outcomes, assessment patterns, or AICTE compliance I can help you with?`;
  }

  private getWebsiteRecommendations(message: string): string {
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('machine learning') || lowerMessage.includes('ml')) {
      return this.getMachineLearningResources();
    }

    if (lowerMessage.includes('data structure')) {
      return this.getDataStructuresResources();
    }

    return `Here are excellent educational websites for course development:

**🌐 General Programming & CS:**
• [Coursera](https://www.coursera.org) - University-level courses from top institutions
• [edX](https://www.edx.org) - MIT, Harvard, and other prestigious university courses
• [Khan Academy](https://www.khanacademy.org) - Free comprehensive learning platform

**💻 Technical Resources:**
• [GeeksforGeeks](https://www.geeksforgeeks.org) - Programming tutorials and interview prep
• [Stack Overflow](https://stackoverflow.com) - Developer community and Q&A
• [GitHub](https://github.com) - Code repositories and open-source projects

**📚 Academic Resources:**
• [IEEE Xplore](https://ieeexplore.ieee.org) - Engineering and technology research papers
• [ACM Digital Library](https://dl.acm.org) - Computer science research and publications
• [Google Scholar](https://scholar.google.com) - Academic search engine

Which specific subject would you like detailed website resources for?`;
  }

  private getTextbookRecommendations(message: string): string {
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('machine learning') || lowerMessage.includes('ml')) {
      return `**📚 Machine Learning Textbooks:**

**Core Textbooks:**
• "Pattern Recognition and Machine Learning" by Christopher Bishop
• "The Elements of Statistical Learning" by Hastie, Tibshirani & Friedman
• "Machine Learning: A Probabilistic Perspective" by Kevin Murphy

**Practical Books:**
• "Hands-On Machine Learning" by Aurélien Géron
• "Python Machine Learning" by Sebastian Raschka
• "Introduction to Statistical Learning" by James, Witten, Hastie & Tibshirani

**Advanced References:**
• "Deep Learning" by Ian Goodfellow, Yoshua Bengio & Aaron Courville
• "Reinforcement Learning: An Introduction" by Sutton & Barto

Would you like specific ISBN numbers or more details about any of these books?`;
    }

    return `**📚 General Computer Science Textbooks:**

**Programming Fundamentals:**
• "Introduction to Algorithms" by Cormen, Leiserson, Rivest & Stein
• "Clean Code" by Robert C. Martin
• "Design Patterns" by Gang of Four

**Database Systems:**
• "Database System Concepts" by Silberschatz, Korth & Sudarshan
• "Fundamentals of Database Systems" by Elmasri & Navathe

**Computer Networks:**
• "Computer Networking: A Top-Down Approach" by Kurose & Ross
• "Computer Networks" by Andrew Tanenbaum

Which specific subject textbooks would you like recommendations for?`;
  }

  private getMachineLearningResources(): string {
    return `**🤖 Machine Learning Resources:**

**🌐 Essential Websites:**
• [Coursera ML Course](https://www.coursera.org/learn/machine-learning) - Andrew Ng's famous course
• [Kaggle Learn](https://www.kaggle.com/learn) - Free micro-courses with hands-on practice
• [Fast.ai](https://www.fast.ai) - Practical deep learning for coders
• [Scikit-learn](https://scikit-learn.org/stable/tutorial/index.html) - Official tutorials and documentation
• [TensorFlow](https://www.tensorflow.org/tutorials) - Google's machine learning platform
• [PyTorch](https://pytorch.org/tutorials/) - Facebook's machine learning framework

**📚 Recommended Textbooks:**
• "Pattern Recognition and Machine Learning" - Christopher Bishop
• "The Elements of Statistical Learning" - Hastie, Tibshirani & Friedman
• "Hands-On Machine Learning" - Aurélien Géron
• "Python Machine Learning" - Sebastian Raschka

**🎯 Practical Resources:**
• [Google Colab](https://colab.research.google.com) - Free GPU/TPU access for ML
• [Jupyter Notebooks](https://jupyter.org) - Interactive development environment
• [Papers with Code](https://paperswithcode.com) - Latest research with implementations

Would you like more specific resources for any ML subtopic like deep learning, NLP, or computer vision?`;
  }

  private getDataStructuresResources(): string {
    return `**🔗 Data Structures & Algorithms Resources:**

**🌐 Learning Websites:**
• [GeeksforGeeks](https://www.geeksforgeeks.org/data-structures/) - Comprehensive tutorials
• [LeetCode](https://leetcode.com/explore/learn/) - Practice problems with solutions
• [HackerRank](https://www.hackerrank.com/domains/data-structures) - Coding challenges
• [VisuAlgo](https://visualgo.net/en) - Algorithm visualizations
• [CS50](https://cs50.harvard.edu/x/2023/) - Harvard's introduction to computer science

**📚 Essential Textbooks:**
• "Introduction to Algorithms" - Cormen, Leiserson, Rivest & Stein (CLRS)
• "Data Structures and Algorithms in Java" - Robert Lafore
• "Algorithms" - Robert Sedgewick & Kevin Wayne
• "Data Structures and Algorithm Analysis" - Mark Allen Weiss

**🎯 Practice Platforms:**
• [Codeforces](https://codeforces.com) - Competitive programming contests
• [AtCoder](https://atcoder.jp) - Algorithm contests with detailed editorials
• [TopCoder](https://www.topcoder.com) - Programming competitions and tutorials

Need help with specific data structures like trees, graphs, or dynamic programming?`;
  }

  clearHistory(): void {
    this.conversationHistory = [];
  }

  getConversationHistory(): ChatMessage[] {
    return [...this.conversationHistory];
  }
}
