import React, { useState } from 'react';
import { FileText, Download, Clock, Target, CheckSquare, X, Sparkles } from 'lucide-react';
import { Course } from '../types/course';
import { QuestionPaper, QuestionGenerationRequest } from '../types/questionPaper';
import { QuestionPaperService } from '../services/questionPaperService';

interface QuestionPaperGeneratorProps {
  course: Course;
  isOpen: boolean;
  onClose: () => void;
}

export const QuestionPaperGenerator: React.FC<QuestionPaperGeneratorProps> = ({
  course,
  isOpen,
  onClose
}) => {
  const [selectedUnits, setSelectedUnits] = useState<number[]>([]);
  const [totalQuestions, setTotalQuestions] = useState(20);
  const [duration, setDuration] = useState(60);
  const [difficulty, setDifficulty] = useState<'mixed' | 'easy' | 'medium' | 'hard'>('mixed');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedPaper, setGeneratedPaper] = useState<QuestionPaper | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  const questionPaperService = new QuestionPaperService();

  if (!isOpen) return null;

  const handleUnitToggle = (unitNumber: number) => {
    setSelectedUnits(prev => 
      prev.includes(unitNumber)
        ? prev.filter(u => u !== unitNumber)
        : [...prev, unitNumber]
    );
  };

  const handleGenerateQuestions = async () => {
    console.log('🎯 Generate Questions button clicked!');
    console.log('Selected units:', selectedUnits);
    console.log('Total questions:', totalQuestions);

    if (selectedUnits.length === 0) {
      alert('Please select at least one unit');
      return;
    }

    setIsGenerating(true);

    try {
      console.log('🚀 Starting question generation process...');

      const request: QuestionGenerationRequest = {
        courseId: course.id,
        courseTitle: course.title,
        courseCode: course.code,
        selectedUnits,
        questionsPerUnit: Math.ceil(totalQuestions / selectedUnits.length),
        totalQuestions,
        difficulty,
        duration,
        instructions: [
          'Answer ALL questions.',
          'Each question carries 1 mark.',
          'Choose the most appropriate answer.',
          'No negative marking.',
          'Use of calculators is not permitted.'
        ]
      };

      console.log('📝 Generating question paper with request:', request);

      const paper = await questionPaperService.generateQuestionPaper(course, request);

      console.log('✅ Question paper generated successfully!');
      console.log('📊 Number of questions:', paper.questions.length);
      console.log('🔑 Sample question:', paper.questions[0]);

      setGeneratedPaper(paper);
      setShowPreview(true);

      console.log('🎉 Preview should now be visible!');

    } catch (error) {
      console.error('❌ Error generating question paper:', error);
      alert(`Failed to generate question paper: ${error.message || error}`);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleExportPDF = () => {
    console.log('📄 Export PDF button clicked!');
    if (generatedPaper) {
      console.log('📋 Exporting question paper:', generatedPaper.title);
      console.log('🔑 Questions with answers:', generatedPaper.questions.length);
      questionPaperService.exportToPDF(generatedPaper);
    } else {
      console.log('❌ No generated paper to export');
    }
  };

  const handleStartNew = () => {
    setGeneratedPaper(null);
    setShowPreview(false);
    setSelectedUnits([]);
  };

  if (showPreview && generatedPaper) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
        <div className="bg-gray-900/95 backdrop-blur-xl rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden border border-gray-700/50">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-700/50">
            <div className="flex items-center gap-3">
              <div className="bg-gradient-to-r from-green-500 to-emerald-500 p-3 rounded-xl">
                <FileText className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-green-300">Question Paper Generated</h2>
                <p className="text-sm text-gray-400">{generatedPaper.questions.length} questions ready</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={handleExportPDF}
                className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-4 py-2 rounded-xl hover:from-blue-600 hover:to-cyan-600 transition-all duration-300 flex items-center gap-2"
              >
                <Download className="w-4 h-4" />
                Export PDF
              </button>
              <button
                onClick={handleStartNew}
                className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-2 rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-300"
              >
                Generate New
              </button>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>

          {/* Question Paper Preview */}
          <div className="p-6 overflow-y-auto max-h-[70vh]">
            <div className="bg-white text-black p-8 rounded-xl">
              <div className="text-center mb-6 border-b-2 border-gray-300 pb-4">
                <h1 className="text-2xl font-bold mb-2">{generatedPaper.title}</h1>
                <div className="text-sm space-y-1">
                  <p><strong>Course:</strong> {generatedPaper.courseTitle} ({generatedPaper.courseCode})</p>
                  <p><strong>Duration:</strong> {generatedPaper.duration} minutes | <strong>Total Marks:</strong> {generatedPaper.totalMarks}</p>
                  <p><strong>Units Covered:</strong> {generatedPaper.selectedUnits.join(', ')}</p>
                </div>
              </div>

              <div className="mb-6 bg-gray-100 p-4 rounded-lg">
                <h3 className="font-bold mb-2">Instructions:</h3>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  {generatedPaper.instructions.map((instruction, index) => (
                    <li key={index}>{instruction}</li>
                  ))}
                </ul>
              </div>

              <div className="space-y-6">
                {generatedPaper.questions.map((question, index) => (
                  <div key={question.id} className="border-b border-gray-200 pb-4">
                    <p className="font-semibold mb-3">
                      <span className="mr-2">Q{index + 1}.</span>
                      {question.question}
                      <span className="float-right text-sm font-normal">[{question.marks} mark]</span>
                    </p>
                    <div className="ml-6 space-y-1">
                      {question.options.map((option, optIndex) => (
                        <p key={option.id} className="text-sm">
                          {String.fromCharCode(97 + optIndex)}) {option.text}
                        </p>
                      ))}
                    </div>
                    <div className="mt-2 text-xs text-gray-600">
                      <em>Topic: {question.topic} | Unit: {question.unit} | Difficulty: {question.difficulty}</em>
                    </div>
                  </div>
                ))}
              </div>

              {/* Answer Key Section */}
              <div className="mt-12 pt-8 border-t-2 border-gray-300">
                <h3 className="text-xl font-bold text-center mb-6 text-red-600 underline">ANSWER KEY</h3>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-400">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="border border-gray-400 px-4 py-3 text-center font-bold">Question No.</th>
                        <th className="border border-gray-400 px-4 py-3 text-center font-bold">Correct Option</th>
                        <th className="border border-gray-400 px-4 py-3 text-left font-bold">Answer</th>
                      </tr>
                    </thead>
                    <tbody>
                      {generatedPaper.questions.map((question, index) => {
                        const correctOptionIndex = question.options.findIndex(option => option.isCorrect);
                        const correctOptionLetter = correctOptionIndex !== -1 ? String.fromCharCode(97 + correctOptionIndex) : 'N/A';

                        return (
                          <tr key={question.id} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                            <td className="border border-gray-400 px-4 py-2 text-center font-semibold">{index + 1}</td>
                            <td className="border border-gray-400 px-4 py-2 text-center font-bold text-red-600">{correctOptionLetter})</td>
                            <td className="border border-gray-400 px-4 py-2 text-left">{question.correctAnswer}</td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
                <div className="mt-4 text-center text-xs text-gray-600 bg-yellow-50 p-3 rounded border">
                  <p><strong>Note:</strong> This answer key is for instructor use only. Do not distribute to students during the examination.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900/95 backdrop-blur-xl rounded-2xl shadow-2xl w-full max-w-2xl max-h-[80vh] overflow-hidden border border-gray-700/50">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700/50">
          <div className="flex items-center gap-3">
            <div className="bg-gradient-to-r from-orange-500 to-red-500 p-3 rounded-xl">
              <FileText className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-orange-300">Generate Question Paper</h2>
              <p className="text-sm text-gray-400">Create MCQ evaluation for {course.title}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          <div className="space-y-6">
            {/* Unit Selection */}
            <div>
              <h3 className="text-lg font-semibold text-cyan-300 mb-4 flex items-center gap-2">
                <CheckSquare className="w-5 h-5" />
                Select Units
              </h3>
              <div className="grid grid-cols-1 gap-3">
                {course.units.map((unit) => (
                  <label
                    key={unit.unitNumber}
                    className="flex items-center gap-3 p-3 bg-gray-800/40 rounded-xl border border-gray-700/50 hover:bg-gray-800/60 transition-all cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      checked={selectedUnits.includes(unit.unitNumber)}
                      onChange={() => handleUnitToggle(unit.unitNumber)}
                      className="w-4 h-4 text-cyan-500 bg-gray-700 border-gray-600 rounded focus:ring-cyan-500"
                    />
                    <div>
                      <p className="font-medium text-white">Unit {unit.unitNumber}: {unit.title}</p>
                      <p className="text-sm text-gray-400">{unit.topics?.length || 0} topics available</p>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Configuration */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  <Target className="w-4 h-4 inline mr-1" />
                  Total Questions
                </label>
                <input
                  type="number"
                  min="10"
                  max="50"
                  value={totalQuestions}
                  onChange={(e) => setTotalQuestions(parseInt(e.target.value))}
                  className="w-full bg-gray-800/50 border border-gray-600 rounded-xl px-3 py-2 text-white focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  <Clock className="w-4 h-4 inline mr-1" />
                  Duration (minutes)
                </label>
                <input
                  type="number"
                  min="30"
                  max="180"
                  value={duration}
                  onChange={(e) => setDuration(parseInt(e.target.value))}
                  className="w-full bg-gray-800/50 border border-gray-600 rounded-xl px-3 py-2 text-white focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Difficulty Level
              </label>
              <select
                value={difficulty}
                onChange={(e) => setDifficulty(e.target.value as any)}
                className="w-full bg-gray-800/50 border border-gray-600 rounded-xl px-3 py-2 text-white focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
              >
                <option value="mixed">Mixed (Easy, Medium, Hard)</option>
                <option value="easy">Easy</option>
                <option value="medium">Medium</option>
                <option value="hard">Hard</option>
              </select>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-8 border-t-4 border-orange-500 bg-gradient-to-r from-gray-800/50 to-gray-900/50">
          <div className="text-center mb-6">
            <h3 className="text-2xl font-bold text-orange-300 mb-3">🚀 Generate Question Paper</h3>
            <p className="text-gray-300">Create your MCQ question paper with automatic answer key</p>
          </div>

          {/* Test Button for Debugging */}
          <button
            onClick={() => {
              console.log('🧪 TEST BUTTON CLICKED!');
              alert('Test button works! Now trying to generate questions...');
              handleGenerateQuestions();
            }}
            className="w-full mb-4 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-bold py-3 px-6 rounded-xl hover:from-blue-600 hover:to-cyan-600 transition-all duration-300 flex items-center justify-center gap-2"
          >
            🧪 TEST GENERATE (Click Me First!)
          </button>

          {/* Main Generate Button */}
          <button
            onClick={() => {
              console.log('🚀 MAIN Generate Question Paper button clicked!');
              console.log('Selected units:', selectedUnits);
              console.log('Total questions:', totalQuestions);
              console.log('Difficulty:', difficulty);
              console.log('Duration:', duration);
              handleGenerateQuestions();
            }}
            disabled={isGenerating || selectedUnits.length === 0}
            className="w-full bg-gradient-to-r from-orange-500 to-red-500 text-white font-bold py-6 px-8 rounded-xl hover:from-orange-600 hover:to-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 flex items-center justify-center gap-3 shadow-2xl transform hover:scale-105 text-xl border-2 border-orange-400"
            title={selectedUnits.length === 0 ? "Please select at least one unit first" : "Generate question paper with answer key"}
          >
            {isGenerating ? (
              <>
                <div className="w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
                <span className="text-xl">Generating Questions...</span>
              </>
            ) : (
              <>
                <Sparkles className="w-8 h-8" />
                <span className="text-xl">🔥 GENERATE QUESTION PAPER 🔥</span>
              </>
            )}
          </button>

          {selectedUnits.length === 0 && (
            <div className="text-center text-red-400 text-base mt-4 bg-red-900/30 p-4 rounded-lg border-2 border-red-500/50">
              <p className="font-bold text-lg">⚠️ SELECT UNITS FIRST</p>
              <p>Please check at least one unit checkbox above to generate questions</p>
            </div>
          )}

          {selectedUnits.length > 0 && (
            <div className="text-center text-green-400 text-base mt-4 bg-green-900/30 p-4 rounded-lg border-2 border-green-500/50">
              <p className="font-bold text-lg">✅ READY TO GENERATE</p>
              <p>Selected {selectedUnits.length} unit(s) • {totalQuestions} questions • {duration} minutes • {difficulty} difficulty</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
