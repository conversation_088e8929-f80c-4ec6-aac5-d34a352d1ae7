export interface CourseTemplate {
  domain: string;
  subjects: string[];
  promptEnhancements: string;
  specificRequirements: string[];
  assessmentGuidelines: string;
  resourceRecommendations: string;
}

export const courseTemplates: CourseTemplate[] = [
  {
    domain: 'Computer Science & Engineering',
    subjects: [
      'programming', 'algorithms', 'data structures', 'software engineering', 'database',
      'computer networks', 'operating systems', 'machine learning', 'artificial intelligence',
      'web development', 'mobile development', 'cybersecurity', 'cloud computing', 'blockchain'
    ],
    promptEnhancements: `
**Computer Science Focus Areas:**
- Emphasize hands-on programming and coding exercises
- Include algorithm analysis and complexity theory
- Focus on software development lifecycle and best practices
- Integrate current industry technologies and frameworks
- Include system design and architecture concepts
- Emphasize problem-solving and computational thinking

**Technical Skills Development:**
- Programming languages: Python, Java, C++, JavaScript
- Development tools: Git, IDEs, debugging tools
- Frameworks and libraries relevant to the subject
- Database design and management
- Testing methodologies and quality assurance`,
    specificRequirements: [
      'Include practical programming assignments in each unit',
      'Provide algorithm complexity analysis where applicable',
      'Reference current industry standards and best practices',
      'Include system design components for advanced courses',
      'Emphasize software engineering principles',
      'Include version control and collaborative development practices'
    ],
    assessmentGuidelines: `
**Assessment Distribution:**
- Programming assignments: 25-30%
- Practical lab work: 20-25%
- Theory examinations: 40-45%
- Project work: 10-15%

**Evaluation Criteria:**
- Code quality and documentation
- Algorithm efficiency and correctness
- Problem-solving approach
- Understanding of theoretical concepts`,
    resourceRecommendations: `
**Recommended Resources:**
- ACM Digital Library and IEEE Xplore for research papers
- GitHub repositories for practical examples
- Online coding platforms (LeetCode, HackerRank, CodeChef)
- Industry documentation and API references
- Open source projects for real-world examples`
  },
  {
    domain: 'Mechanical Engineering',
    subjects: [
      'thermodynamics', 'fluid mechanics', 'heat transfer', 'manufacturing', 'design',
      'materials', 'mechanics', 'vibrations', 'control systems', 'robotics', 'automotive',
      'aerospace', 'energy systems', 'cad', 'cam'
    ],
    promptEnhancements: `
**Mechanical Engineering Focus Areas:**
- Emphasize fundamental engineering principles and their applications
- Include design thinking and engineering problem-solving
- Focus on manufacturing processes and material selection
- Integrate CAD/CAM tools and simulation software
- Include sustainability and environmental considerations
- Emphasize safety standards and regulations

**Technical Skills Development:**
- CAD software: SolidWorks, AutoCAD, CATIA
- Simulation tools: ANSYS, MATLAB/Simulink
- Manufacturing processes and quality control
- Material testing and characterization
- Project management and engineering economics`,
    specificRequirements: [
      'Include design projects and case studies',
      'Provide hands-on laboratory experiments',
      'Reference relevant IS/ISO/ASME standards',
      'Include sustainability and environmental impact analysis',
      'Emphasize safety protocols and risk assessment',
      'Include cost analysis and optimization techniques'
    ],
    assessmentGuidelines: `
**Assessment Distribution:**
- Design projects: 25-30%
- Laboratory work: 20-25%
- Theory examinations: 40-45%
- Technical reports: 10-15%

**Evaluation Criteria:**
- Design methodology and innovation
- Technical accuracy and calculations
- Laboratory skills and data analysis
- Communication and presentation skills`,
    resourceRecommendations: `
**Recommended Resources:**
- ASME and SAE technical papers and standards
- Engineering handbooks and design guides
- CAD/CAM software tutorials and documentation
- Industry case studies and best practices
- Professional engineering journals and magazines`
  },
  {
    domain: 'Electrical & Electronics Engineering',
    subjects: [
      'circuits', 'electronics', 'power systems', 'control systems', 'communications',
      'signal processing', 'microprocessors', 'vlsi', 'embedded systems', 'renewable energy',
      'power electronics', 'instrumentation', 'electromagnetics', 'digital systems'
    ],
    promptEnhancements: `
**Electrical Engineering Focus Areas:**
- Emphasize circuit analysis and design principles
- Include practical electronics and hardware implementation
- Focus on power systems and energy efficiency
- Integrate modern control theory and applications
- Include communication systems and signal processing
- Emphasize safety in electrical systems

**Technical Skills Development:**
- Circuit simulation: SPICE, Multisim, LTSpice
- Programming: C/C++, Python, MATLAB
- Hardware design and PCB layout
- Measurement and instrumentation
- Power system analysis tools`,
    specificRequirements: [
      'Include circuit design and simulation exercises',
      'Provide hands-on hardware implementation',
      'Reference IEEE standards and electrical codes',
      'Include power quality and efficiency analysis',
      'Emphasize electrical safety and protection',
      'Include renewable energy and sustainability aspects'
    ],
    assessmentGuidelines: `
**Assessment Distribution:**
- Circuit design projects: 25-30%
- Laboratory experiments: 25-30%
- Theory examinations: 35-40%
- Technical presentations: 10-15%

**Evaluation Criteria:**
- Circuit analysis and design skills
- Hardware implementation and troubleshooting
- Understanding of electrical principles
- Safety awareness and practices`,
    resourceRecommendations: `
**Recommended Resources:**
- IEEE standards and technical papers
- Electronic component datasheets and application notes
- Circuit simulation software documentation
- Power system analysis tools and software
- Professional electrical engineering journals`
  },
  {
    domain: 'Civil Engineering',
    subjects: [
      'structural', 'geotechnical', 'transportation', 'environmental', 'water resources',
      'construction', 'surveying', 'concrete', 'steel', 'earthquake', 'foundation',
      'highway', 'bridge', 'building', 'infrastructure'
    ],
    promptEnhancements: `
**Civil Engineering Focus Areas:**
- Emphasize structural analysis and design principles
- Include construction management and project planning
- Focus on sustainable and green building practices
- Integrate modern construction technologies
- Include infrastructure development and maintenance
- Emphasize safety and regulatory compliance

**Technical Skills Development:**
- Structural analysis software: STAAD Pro, ETABS, SAP2000
- CAD software: AutoCAD, Revit, SketchUp
- Project management tools and techniques
- Surveying instruments and GPS technology
- Material testing and quality control`,
    specificRequirements: [
      'Include structural design projects and calculations',
      'Provide field work and site visits',
      'Reference IS codes and construction standards',
      'Include environmental impact assessment',
      'Emphasize construction safety and quality control',
      'Include cost estimation and project management'
    ],
    assessmentGuidelines: `
**Assessment Distribution:**
- Design projects: 30-35%
- Field work and surveys: 20-25%
- Theory examinations: 35-40%
- Technical reports: 10-15%

**Evaluation Criteria:**
- Design methodology and code compliance
- Field work accuracy and data collection
- Understanding of construction principles
- Project management and cost analysis`,
    resourceRecommendations: `
**Recommended Resources:**
- IS codes and construction standards
- Structural engineering handbooks and guides
- Construction management best practices
- Environmental engineering guidelines
- Professional civil engineering journals`
  },
  {
    domain: 'Mathematics & Applied Sciences',
    subjects: [
      'calculus', 'algebra', 'statistics', 'probability', 'differential equations',
      'numerical methods', 'discrete mathematics', 'linear algebra', 'complex analysis',
      'real analysis', 'topology', 'geometry', 'optimization', 'mathematical modeling'
    ],
    promptEnhancements: `
**Mathematics Focus Areas:**
- Emphasize rigorous mathematical proofs and derivations
- Include computational methods and numerical solutions
- Focus on real-world applications and modeling
- Integrate mathematical software and tools
- Include interdisciplinary connections
- Emphasize problem-solving strategies

**Technical Skills Development:**
- Mathematical software: MATLAB, Mathematica, R, Python
- Statistical analysis and data visualization
- Numerical computation and simulation
- Mathematical modeling techniques
- Research methodology and academic writing`,
    specificRequirements: [
      'Include rigorous mathematical proofs and derivations',
      'Provide computational exercises and programming',
      'Reference standard mathematical texts and journals',
      'Include applications to engineering and science',
      'Emphasize logical reasoning and analytical thinking',
      'Include research-oriented problems and projects'
    ],
    assessmentGuidelines: `
**Assessment Distribution:**
- Problem-solving assignments: 30-35%
- Computational projects: 20-25%
- Theory examinations: 40-45%
- Research presentations: 5-10%

**Evaluation Criteria:**
- Mathematical rigor and proof techniques
- Computational skills and software usage
- Problem-solving approach and creativity
- Understanding of theoretical concepts`,
    resourceRecommendations: `
**Recommended Resources:**
- Standard mathematical textbooks and references
- Mathematical journals and research papers
- Computational software documentation
- Online mathematical resources and databases
- Professional mathematical society publications`
  },
  {
    domain: 'Management & Business Studies',
    subjects: [
      'management', 'marketing', 'finance', 'accounting', 'human resources',
      'operations', 'strategy', 'entrepreneurship', 'economics', 'business law',
      'organizational behavior', 'supply chain', 'project management', 'leadership'
    ],
    promptEnhancements: `
**Business Management Focus Areas:**
- Emphasize practical business applications and case studies
- Include current industry trends and best practices
- Focus on leadership and decision-making skills
- Integrate technology and digital transformation
- Include global business perspectives
- Emphasize ethical business practices

**Professional Skills Development:**
- Business analytics and data interpretation
- Communication and presentation skills
- Strategic thinking and planning
- Team leadership and collaboration
- Financial analysis and budgeting`,
    specificRequirements: [
      'Include real business case studies and scenarios',
      'Provide industry interaction and guest lectures',
      'Reference current business practices and trends',
      'Include ethical considerations and corporate responsibility',
      'Emphasize practical skills and employability',
      'Include global business perspectives and cultural awareness'
    ],
    assessmentGuidelines: `
**Assessment Distribution:**
- Case study analysis: 25-30%
- Group projects: 20-25%
- Theory examinations: 35-40%
- Presentations: 15-20%

**Evaluation Criteria:**
- Analytical and critical thinking skills
- Business acumen and practical application
- Communication and presentation abilities
- Team collaboration and leadership`,
    resourceRecommendations: `
**Recommended Resources:**
- Harvard Business Review and business journals
- Industry reports and market research
- Business case study databases
- Professional business publications
- Corporate annual reports and financial statements`
  }
];

export function getTemplateForSubject(subject: string, department: string): CourseTemplate | null {
  const subjectLower = subject.toLowerCase();
  const departmentLower = department.toLowerCase();
  
  // Find the most relevant template based on subject and department
  for (const template of courseTemplates) {
    // Check if subject matches any keywords in the template
    if (template.subjects.some(keyword => 
      subjectLower.includes(keyword) || keyword.includes(subjectLower)
    )) {
      return template;
    }
    
    // Check if department matches the domain
    if (departmentLower.includes(template.domain.toLowerCase().split(' ')[0])) {
      return template;
    }
  }
  
  // Default fallback - return a general engineering template
  return courseTemplates.find(t => t.domain === 'Computer Science & Engineering') || null;
}

export function enhancePromptWithTemplate(
  basePrompt: string, 
  template: CourseTemplate | null,
  subject: string
): string {
  if (!template) {
    return basePrompt;
  }
  
  return `${basePrompt}

## Domain-Specific Guidelines for ${template.domain}

${template.promptEnhancements}

## Specific Requirements for ${subject}:
${template.specificRequirements.map(req => `- ${req}`).join('\n')}

## Assessment Guidelines:
${template.assessmentGuidelines}

## Resource Recommendations:
${template.resourceRecommendations}

**Important:** Ensure all content is tailored specifically for ${subject} while following the above domain guidelines.`;
}
