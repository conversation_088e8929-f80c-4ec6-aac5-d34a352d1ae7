import React, { useState } from 'react';
import { <PERSON><PERSON>les, Brain, MessageSquare, Settings, Zap, CheckCircle } from 'lucide-react';

interface LangChainDemoProps {
  isOpen: boolean;
  onClose: () => void;
}

export const LangChainDemo: React.FC<LangChainDemoProps> = ({ isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState<'features' | 'comparison' | 'examples'>('features');

  if (!isOpen) return null;

  const features = [
    {
      icon: Brain,
      title: 'Conversation Memory',
      description: 'Maintains context across the entire conversation, remembering previous questions and building upon them.',
      example: 'Ask about AICTE guidelines, then follow up with "How does this apply to my CS course?" - it remembers the context!'
    },
    {
      icon: MessageSquare,
      title: 'Structured Output Parsing',
      description: 'Uses Zod schemas to ensure consistent, well-formatted responses for course generation.',
      example: 'Course syllabi are generated with guaranteed structure and all required fields.'
    },
    {
      icon: Settings,
      title: 'Advanced Prompt Engineering',
      description: 'Sophisticated prompt templates with dynamic context injection for better responses.',
      example: 'Prompts adapt based on your academic level, department, and previous conversation.'
    },
    {
      icon: Zap,
      title: 'Agent-Based Architecture',
      description: 'Uses LangChain agents with specialized tools for industry research and compliance checking.',
      example: 'Automatically researches current industry trends when generating course content.'
    }
  ];

  const comparison = [
    {
      feature: 'Conversation Memory',
      traditional: '❌ No memory between messages',
      langchain: '✅ Full conversation context maintained'
    },
    {
      feature: 'Response Quality',
      traditional: '⚡ Fast but basic responses',
      langchain: '🧠 Contextual, intelligent responses'
    },
    {
      feature: 'Error Handling',
      traditional: '⚠️ Basic error messages',
      langchain: '🛡️ Robust parsing with fallbacks'
    },
    {
      feature: 'Customization',
      traditional: '📝 Simple prompt templates',
      langchain: '🎯 Dynamic, context-aware prompts'
    },
    {
      feature: 'Tool Integration',
      traditional: '🔧 Manual API calls',
      langchain: '🤖 Intelligent agent with specialized tools'
    }
  ];

  const examples = [
    {
      title: 'Memory in Action',
      conversation: [
        { role: 'user', text: 'What are the AICTE credit requirements for undergraduate courses?' },
        { role: 'bot', text: 'AICTE requires minimum 160 credits for undergraduate programs...' },
        { role: 'user', text: 'How should I distribute these across 8 semesters?' },
        { role: 'bot', text: 'Based on the 160 credits we discussed, you should aim for 20-24 credits per semester...' }
      ]
    },
    {
      title: 'Structured Course Generation',
      conversation: [
        { role: 'user', text: 'Generate a Machine Learning course for 6th semester CS students' },
        { role: 'bot', text: 'I\'ll create a comprehensive ML course. Let me research current industry trends and ensure AICTE compliance...\n\n✅ Industry research completed\n✅ AICTE compliance verified\n✅ Learning outcomes generated\n\nHere\'s your structured course syllabus...' }
      ]
    }
  ];

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900/95 backdrop-blur-xl rounded-2xl shadow-2xl w-full max-w-4xl max-h-[80vh] overflow-hidden border border-gray-700/50">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700/50">
          <div className="flex items-center gap-3">
            <div className="bg-gradient-to-r from-cyan-500 to-blue-500 p-3 rounded-xl">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-cyan-300">LangChain Integration</h2>
              <p className="text-sm text-gray-400">Enhanced AI capabilities for course design</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800"
          >
            ✕
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-700/50">
          {[
            { id: 'features', label: 'Features', icon: Sparkles },
            { id: 'comparison', label: 'Comparison', icon: CheckCircle },
            { id: 'examples', label: 'Examples', icon: MessageSquare }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 px-6 py-3 text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'text-cyan-300 border-b-2 border-cyan-500 bg-gray-800/50'
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {activeTab === 'features' && (
            <div className="space-y-6">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-cyan-300 mb-2">LangChain Enhanced Features</h3>
                <p className="text-gray-400">Discover the advanced capabilities powered by LangChain</p>
              </div>
              
              <div className="grid md:grid-cols-2 gap-6">
                {features.map((feature, index) => (
                  <div key={index} className="bg-gray-800/40 rounded-xl p-6 border border-gray-700/50">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="bg-gradient-to-r from-cyan-500 to-blue-500 p-2 rounded-lg">
                        <feature.icon className="w-5 h-5 text-white" />
                      </div>
                      <h4 className="font-semibold text-cyan-300">{feature.title}</h4>
                    </div>
                    <p className="text-gray-300 mb-3">{feature.description}</p>
                    <div className="bg-gray-900/50 rounded-lg p-3 border-l-4 border-cyan-500">
                      <p className="text-sm text-gray-400 italic">{feature.example}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'comparison' && (
            <div className="space-y-6">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-cyan-300 mb-2">Traditional vs LangChain</h3>
                <p className="text-gray-400">See the difference LangChain makes</p>
              </div>
              
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-700">
                      <th className="text-left py-3 px-4 text-cyan-300">Feature</th>
                      <th className="text-left py-3 px-4 text-orange-300">Traditional Approach</th>
                      <th className="text-left py-3 px-4 text-green-300">LangChain Enhanced</th>
                    </tr>
                  </thead>
                  <tbody>
                    {comparison.map((item, index) => (
                      <tr key={index} className="border-b border-gray-800">
                        <td className="py-4 px-4 font-medium text-gray-300">{item.feature}</td>
                        <td className="py-4 px-4 text-gray-400">{item.traditional}</td>
                        <td className="py-4 px-4 text-gray-400">{item.langchain}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'examples' && (
            <div className="space-y-6">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-cyan-300 mb-2">LangChain in Action</h3>
                <p className="text-gray-400">Real examples of enhanced capabilities</p>
              </div>
              
              {examples.map((example, index) => (
                <div key={index} className="bg-gray-800/40 rounded-xl p-6 border border-gray-700/50">
                  <h4 className="font-semibold text-cyan-300 mb-4">{example.title}</h4>
                  <div className="space-y-3">
                    {example.conversation.map((msg, msgIndex) => (
                      <div key={msgIndex} className={`flex gap-3 ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                        <div className={`max-w-[80%] p-3 rounded-lg ${
                          msg.role === 'user' 
                            ? 'bg-cyan-500/20 text-cyan-100 border border-cyan-500/30' 
                            : 'bg-gray-700/50 text-gray-200 border border-gray-600/30'
                        }`}>
                          <p className="text-sm">{msg.text}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-700/50 p-4 bg-gray-800/30">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-400">
              🚀 Ready to experience enhanced AI capabilities?
            </div>
            <button
              onClick={onClose}
              className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-4 py-2 rounded-lg hover:from-cyan-600 hover:to-blue-600 transition-all duration-200 text-sm font-medium"
            >
              Try LangChain Mode
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
