import { Question<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, QuestionGenerationRequest, MCQO<PERSON> } from '../types/questionPaper';
import { Course, Unit } from '../types/course';

export class QuestionPaperService {
  private generateRandomId(): string {
    return `qp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateQuestionId(): string {
    return `q_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  private generateOptionId(): string {
    return `opt_${Date.now()}_${Math.random().toString(36).substr(2, 4)}`;
  }

  private normalizeSubjectName(subject: string): string {
    // Create a mapping of common variations to standard names
    const subjectMappings: { [key: string]: string } = {
      'machine learning': 'Machine Learning',
      'ml': 'Machine Learning',
      'data structures': 'Data Structures',
      'ds': 'Data Structures',
      'operating systems': 'Operating Systems',
      'os': 'Operating Systems',
      'computer networks': 'Computer Networks',
      'networks': 'Computer Networks',
      'cn': 'Computer Networks',
      'database management systems': 'Database Management Systems',
      'dbms': 'Database Management Systems',
      'database': 'Database Management Systems',
      'software engineering': 'Software Engineering',
      'se': 'Software Engineering',
      'web development': 'Web Development',
      'web dev': 'Web Development',
      'artificial intelligence': 'Artificial Intelligence',
      'ai': 'Artificial Intelligence',
      'computer graphics': 'Computer Graphics',
      'graphics': 'Computer Graphics',
      'cg': 'Computer Graphics',
      'cybersecurity': 'Cybersecurity',
      'cyber security': 'Cybersecurity',
      'security': 'Cybersecurity',
      'mobile app development': 'Mobile App Development',
      'mobile development': 'Mobile App Development',
      'app development': 'Mobile App Development',
      'cloud computing': 'Cloud Computing',
      'cloud': 'Cloud Computing'
    };

    const normalized = subject.toLowerCase().trim();
    return subjectMappings[normalized] || subject;
  }

  private getQuestionBank(subject: string): { [unitNumber: number]: MCQQuestion[] } {
    // Normalize subject name for better matching (same as in mockLLMService)
    const normalizedSubject = this.normalizeSubjectName(subject);

    // This is a comprehensive question bank for different subjects
    const questionBanks: { [subject: string]: { [unitNumber: number]: MCQQuestion[] } } = {
      'Machine Learning': {
        1: [
          {
            id: this.generateQuestionId(),
            question: 'Which of the following is NOT a type of machine learning?',
            options: [
              { id: this.generateOptionId(), text: 'Supervised Learning', isCorrect: false },
              { id: this.generateOptionId(), text: 'Unsupervised Learning', isCorrect: false },
              { id: this.generateOptionId(), text: 'Reinforcement Learning', isCorrect: false },
              { id: this.generateOptionId(), text: 'Deterministic Learning', isCorrect: true }
            ],
            correctAnswer: 'Deterministic Learning',
            difficulty: 'easy',
            topic: 'Introduction to Machine Learning',
            unit: 1,
            bloomsLevel: 'remember',
            marks: 1
          },
          {
            id: this.generateQuestionId(),
            question: 'In Principal Component Analysis (PCA), what does the first principal component represent?',
            options: [
              { id: this.generateOptionId(), text: 'The direction of minimum variance', isCorrect: false },
              { id: this.generateOptionId(), text: 'The direction of maximum variance', isCorrect: true },
              { id: this.generateOptionId(), text: 'The mean of the dataset', isCorrect: false },
              { id: this.generateOptionId(), text: 'The median of the dataset', isCorrect: false }
            ],
            correctAnswer: 'The direction of maximum variance',
            difficulty: 'medium',
            topic: 'Principal Component Analysis (PCA)',
            unit: 1,
            bloomsLevel: 'understand',
            marks: 1
          }
        ],
        2: [
          {
            id: this.generateQuestionId(),
            question: 'Which algorithm is used for classification problems?',
            options: [
              { id: this.generateOptionId(), text: 'Linear Regression', isCorrect: false },
              { id: this.generateOptionId(), text: 'K-Means Clustering', isCorrect: false },
              { id: this.generateOptionId(), text: 'Support Vector Machine', isCorrect: true },
              { id: this.generateOptionId(), text: 'DBSCAN', isCorrect: false }
            ],
            correctAnswer: 'Support Vector Machine',
            difficulty: 'easy',
            topic: 'Supervised Learning Algorithms',
            unit: 2,
            bloomsLevel: 'remember',
            marks: 1
          }
        ]
      },
      'Operating Systems': {
        1: [
          {
            id: this.generateQuestionId(),
            question: 'Which scheduling algorithm can cause starvation?',
            options: [
              { id: this.generateOptionId(), text: 'First Come First Serve (FCFS)', isCorrect: false },
              { id: this.generateOptionId(), text: 'Round Robin', isCorrect: false },
              { id: this.generateOptionId(), text: 'Shortest Job First (SJF)', isCorrect: true },
              { id: this.generateOptionId(), text: 'Priority Scheduling with Aging', isCorrect: false }
            ],
            correctAnswer: 'Shortest Job First (SJF)',
            difficulty: 'medium',
            topic: 'CPU Scheduling Algorithms',
            unit: 1,
            bloomsLevel: 'analyze',
            marks: 1
          }
        ],
        2: [
          {
            id: this.generateQuestionId(),
            question: 'What is the purpose of the LRU page replacement algorithm?',
            options: [
              { id: this.generateOptionId(), text: 'Replace the page that will be used farthest in future', isCorrect: false },
              { id: this.generateOptionId(), text: 'Replace the page that was least recently used', isCorrect: true },
              { id: this.generateOptionId(), text: 'Replace the first page that was loaded', isCorrect: false },
              { id: this.generateOptionId(), text: 'Replace a random page', isCorrect: false }
            ],
            correctAnswer: 'Replace the page that was least recently used',
            difficulty: 'medium',
            topic: 'Memory Management',
            unit: 2,
            bloomsLevel: 'understand',
            marks: 1
          }
        ]
      },
      'Data Structures': {
        1: [
          {
            id: this.generateQuestionId(),
            question: 'What is the time complexity of searching in a Binary Search Tree in the worst case?',
            options: [
              { id: this.generateOptionId(), text: 'O(1)', isCorrect: false },
              { id: this.generateOptionId(), text: 'O(log n)', isCorrect: false },
              { id: this.generateOptionId(), text: 'O(n)', isCorrect: true },
              { id: this.generateOptionId(), text: 'O(n²)', isCorrect: false }
            ],
            correctAnswer: 'O(n)',
            difficulty: 'medium',
            topic: 'Binary Search Trees',
            unit: 1,
            bloomsLevel: 'analyze',
            marks: 1
          }
        ],
        2: [
          {
            id: this.generateQuestionId(),
            question: 'Which data structure is used to implement recursion?',
            options: [
              { id: this.generateOptionId(), text: 'Queue', isCorrect: false },
              { id: this.generateOptionId(), text: 'Stack', isCorrect: true },
              { id: this.generateOptionId(), text: 'Array', isCorrect: false },
              { id: this.generateOptionId(), text: 'Linked List', isCorrect: false }
            ],
            correctAnswer: 'Stack',
            difficulty: 'easy',
            topic: 'Stack Implementation',
            unit: 2,
            bloomsLevel: 'remember',
            marks: 1
          }
        ]
      },
      'Computer Networks': {
        1: [
          {
            id: this.generateQuestionId(),
            question: 'Which layer of the OSI model is responsible for routing?',
            options: [
              { id: this.generateOptionId(), text: 'Physical Layer', isCorrect: false },
              { id: this.generateOptionId(), text: 'Data Link Layer', isCorrect: false },
              { id: this.generateOptionId(), text: 'Network Layer', isCorrect: true },
              { id: this.generateOptionId(), text: 'Transport Layer', isCorrect: false }
            ],
            correctAnswer: 'Network Layer',
            difficulty: 'easy',
            topic: 'OSI Model',
            unit: 1,
            bloomsLevel: 'remember',
            marks: 1
          }
        ]
      },
      'Database Management Systems': {
        1: [
          {
            id: this.generateQuestionId(),
            question: 'Which normal form eliminates partial dependencies?',
            options: [
              { id: this.generateOptionId(), text: '1NF', isCorrect: false },
              { id: this.generateOptionId(), text: '2NF', isCorrect: true },
              { id: this.generateOptionId(), text: '3NF', isCorrect: false },
              { id: this.generateOptionId(), text: 'BCNF', isCorrect: false }
            ],
            correctAnswer: '2NF',
            difficulty: 'medium',
            topic: 'Database Normalization',
            unit: 1,
            bloomsLevel: 'understand',
            marks: 1
          }
        ]
      }
    };

    return questionBanks[normalizedSubject] || {};
  }

  private generateGenericQuestions(unit: Unit, unitNumber: number, count: number): MCQQuestion[] {
    const questions: MCQQuestion[] = [];
    const topics = unit.topics || [];
    
    for (let i = 0; i < count && i < topics.length; i++) {
      const topic = topics[i];
      const question: MCQQuestion = {
        id: this.generateQuestionId(),
        question: `Which of the following best describes ${topic}?`,
        options: [
          { id: this.generateOptionId(), text: `${topic} is a fundamental concept in computer science`, isCorrect: true },
          { id: this.generateOptionId(), text: `${topic} is not relevant to this course`, isCorrect: false },
          { id: this.generateOptionId(), text: `${topic} is only used in theoretical applications`, isCorrect: false },
          { id: this.generateOptionId(), text: `${topic} is deprecated and no longer used`, isCorrect: false }
        ],
        correctAnswer: `${topic} is a fundamental concept in computer science`,
        difficulty: 'easy',
        topic: topic,
        unit: unitNumber,
        bloomsLevel: 'remember',
        marks: 1
      };
      questions.push(question);
    }

    return questions;
  }

  async generateQuestionPaper(course: Course, request: QuestionGenerationRequest): Promise<QuestionPaper> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    const questionBank = this.getQuestionBank(course.title);
    const allQuestions: MCQQuestion[] = [];

    // Generate questions for each selected unit
    for (const unitNumber of request.selectedUnits) {
      const unit = course.units.find(u => u.unitNumber === unitNumber);
      if (!unit) continue;

      const questionsPerUnit = Math.ceil(request.totalQuestions / request.selectedUnits.length);
      
      // Try to get questions from question bank first
      const bankQuestions = questionBank[unitNumber] || [];
      const availableQuestions = bankQuestions.slice(0, questionsPerUnit);
      
      // If we need more questions, generate generic ones
      const remainingCount = questionsPerUnit - availableQuestions.length;
      if (remainingCount > 0) {
        const genericQuestions = this.generateGenericQuestions(unit, unitNumber, remainingCount);
        availableQuestions.push(...genericQuestions);
      }

      allQuestions.push(...availableQuestions);
    }

    // Shuffle questions and take only the requested amount
    const shuffledQuestions = allQuestions.sort(() => Math.random() - 0.5);
    const selectedQuestions = shuffledQuestions.slice(0, request.totalQuestions);

    const questionPaper: QuestionPaper = {
      id: this.generateRandomId(),
      title: `${course.title} - Evaluation Question Paper`,
      courseTitle: course.title,
      courseCode: course.code,
      duration: request.duration,
      totalMarks: selectedQuestions.reduce((sum, q) => sum + q.marks, 0),
      instructions: request.instructions || [
        'Answer ALL questions.',
        'Each question carries 1 mark.',
        'Choose the most appropriate answer.',
        'No negative marking.',
        'Use of calculators is not permitted.'
      ],
      questions: selectedQuestions,
      selectedUnits: request.selectedUnits,
      createdAt: new Date(),
      generatedBy: 'CourseCraft AI Question Generator'
    };

    return questionPaper;
  }

  exportToPDF(questionPaper: QuestionPaper): void {
    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    const html = this.generatePDFHTML(questionPaper);
    printWindow.document.write(html);
    printWindow.document.close();
    
    // Wait for content to load then print
    printWindow.onload = () => {
      printWindow.print();
    };
  }

  private generatePDFHTML(qp: QuestionPaper): string {
    const questionsHTML = qp.questions.map((question, index) => `
      <div class="question">
        <p class="question-number"><strong>Q${index + 1}.</strong> ${question.question} <span class="marks">[${question.marks} mark]</span></p>
        <div class="options">
          ${question.options.map((option, optIndex) => `
            <p class="option">${String.fromCharCode(97 + optIndex)}) ${option.text}</p>
          `).join('')}
        </div>
        <div class="topic-info">
          <small><em>Topic: ${question.topic} | Unit: ${question.unit} | Difficulty: ${question.difficulty}</em></small>
        </div>
      </div>
    `).join('');

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${qp.title}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
          .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
          .course-info { margin-bottom: 20px; }
          .instructions { margin-bottom: 30px; background: #f5f5f5; padding: 15px; border-radius: 5px; }
          .question { margin-bottom: 25px; page-break-inside: avoid; }
          .question-number { font-weight: bold; margin-bottom: 10px; }
          .marks { float: right; font-weight: normal; }
          .options { margin-left: 20px; }
          .option { margin: 5px 0; }
          .topic-info { margin-top: 10px; color: #666; font-size: 0.9em; }
          @media print { 
            body { margin: 15px; }
            .question { page-break-inside: avoid; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${qp.title}</h1>
          <div class="course-info">
            <p><strong>Course:</strong> ${qp.courseTitle} (${qp.courseCode})</p>
            <p><strong>Duration:</strong> ${qp.duration} minutes | <strong>Total Marks:</strong> ${qp.totalMarks}</p>
            <p><strong>Units Covered:</strong> ${qp.selectedUnits.join(', ')}</p>
            <p><strong>Generated on:</strong> ${qp.createdAt.toLocaleDateString()}</p>
          </div>
        </div>

        <div class="instructions">
          <h3>Instructions:</h3>
          <ul>
            ${qp.instructions.map(instruction => `<li>${instruction}</li>`).join('')}
          </ul>
        </div>

        <div class="questions">
          ${questionsHTML}
        </div>

        <div style="margin-top: 40px; text-align: center; color: #666; font-size: 0.9em;">
          <p>Generated by CourseCraft AI Question Generator</p>
        </div>
      </body>
      </html>
    `;
  }
}
