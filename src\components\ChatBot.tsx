import React, { useState, useRef, useEffect } from 'react';
import { MessageCircle, X, Send, Bot, User, Spark<PERSON>, Settings } from 'lucide-react';
import { ChatbotService, ChatMessage } from '../services/chatbotService';
import { LangChainChatbotService } from '../services/langchainChatbotService';
import { LLMProvider } from '../services/llmService';

interface ChatBotProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ChatBot: React.FC<ChatBotProps> = ({ isOpen, onClose }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      text: 'Hello! I\'m your CourseCraft AI Assistant powered by LangChain. I can help you with curriculum design, AICTE guidelines, learning outcomes, assessment patterns, and much more. How can I assist you today?',
      sender: 'bot',
      timestamp: new Date()
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [chatbotService, setChatbotService] = useState<ChatbotService | null>(null);
  const [langchainService, setLangchainService] = useState<LangChainChatbotService | null>(null);
  const [useLangChain, setUseLangChain] = useState(true);
  const [showSettings, setShowSettings] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Initialize chatbot services
    const initializeChatbot = () => {
      const storedApiKey = localStorage.getItem('openai_api_key');
      const storedProvider = localStorage.getItem('llm_provider') as LLMProvider || 'openai';
      const useLangChainStored = localStorage.getItem('use_langchain') !== 'false';

      setUseLangChain(useLangChainStored);

      if (storedApiKey && storedApiKey !== 'demo') {
        // Initialize traditional chatbot service
        setChatbotService(new ChatbotService({
          useLLM: true,
          llmConfig: {
            provider: storedProvider,
            apiKey: storedApiKey,
            temperature: 0.7,
            maxTokens: 1000
          }
        }));

        // Initialize LangChain chatbot service
        try {
          setLangchainService(new LangChainChatbotService({
            llmConfig: {
              provider: storedProvider,
              apiKey: storedApiKey,
              temperature: 0.7,
              maxTokens: 1000
            },
            memoryType: 'buffer', // or 'summary' for longer conversations
            maxTokenLimit: 4000
          }));
        } catch (error) {
          console.error('Failed to initialize LangChain service:', error);
          setUseLangChain(false);
        }
      } else {
        setChatbotService(new ChatbotService({ useLLM: false }));
      }
    };

    initializeChatbot();
  }, []);



  const handleSendMessage = async () => {
    if (!inputText.trim()) return;

    // Check if we have any service available
    const activeService = useLangChain && langchainService ? langchainService : chatbotService;
    if (!activeService) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: inputText,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputText;
    setInputText('');
    setIsTyping(true);

    try {
      const response = await activeService.generateResponse(currentInput);

      const botResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: response,
        sender: 'bot',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botResponse]);
    } catch (error) {
      console.error('Error generating chatbot response:', error);
      const errorResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: `I apologize, but I'm having trouble processing your request right now. ${useLangChain ? '(LangChain service error)' : '(Traditional service error)'} Please try again or ask about course design topics like AICTE guidelines, curriculum structure, or assessment patterns.`,
        sender: 'bot',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorResponse]);
    } finally {
      setIsTyping(false);
    }
  };

  const toggleLangChain = () => {
    const newValue = !useLangChain;
    setUseLangChain(newValue);
    localStorage.setItem('use_langchain', newValue.toString());

    // Add a system message to indicate the switch
    const systemMessage: ChatMessage = {
      id: Date.now().toString(),
      text: `Switched to ${newValue ? 'LangChain-powered' : 'traditional'} chatbot service. ${newValue ? 'Enhanced memory and conversation capabilities enabled!' : 'Using rule-based responses with basic LLM integration.'}`,
      sender: 'bot',
      timestamp: new Date()
    };
    setMessages(prev => [...prev, systemMessage]);
  };

  const clearConversation = async () => {
    if (useLangChain && langchainService) {
      await langchainService.clearMemory();
    }
    setMessages([{
      id: '1',
      text: `Hello! I'm your CourseCraft AI Assistant${useLangChain ? ' powered by LangChain' : ''}. I can help you with curriculum design, AICTE guidelines, learning outcomes, assessment patterns, and much more. How can I assist you today?`,
      sender: 'bot',
      timestamp: new Date()
    }]);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900/95 backdrop-blur-xl rounded-2xl shadow-2xl w-full max-w-md h-[600px] flex flex-col border border-gray-700/50">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700/50">
          <div className="flex items-center gap-3">
            <div className="relative">
              <div className="bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-500 p-1 rounded-xl shadow-lg">
                <div className="bg-gray-900 p-1.5 rounded-lg">
                  <img
                    src="/logomini.jpeg"
                    alt="CourseCraft"
                    className="w-6 h-6 rounded-lg object-cover"
                  />
                </div>
              </div>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            </div>
            <div>
              <h3 className="font-bold text-cyan-300 text-lg">CourseCraft AI</h3>
              <p className="text-xs text-gray-400 flex items-center gap-1 font-medium">
                {useLangChain && langchainService ? (
                  <>
                    <Sparkles className="w-3 h-3 text-cyan-400" />
                    LangChain Enhanced
                  </>
                ) : chatbotService && localStorage.getItem('openai_api_key') !== 'demo' && localStorage.getItem('openai_api_key') ? (
                  <>
                    <Sparkles className="w-3 h-3" />
                    AI-Enhanced
                  </>
                ) : (
                  'Smart Assistant'
                )}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="text-gray-400 hover:text-cyan-300 p-1 hover:bg-gray-700/50 rounded-lg transition-all duration-200"
              title="Settings"
            >
              <Settings className="w-4 h-4" />
            </button>
            <button
              onClick={clearConversation}
              className="text-gray-400 hover:text-gray-300 p-1 hover:bg-gray-700/50 rounded-lg transition-all duration-200 text-xs"
              title="Clear conversation"
            >
              Clear
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors p-1 rounded-lg hover:bg-gray-800"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Settings Panel */}
        {showSettings && (
          <div className="border-b border-gray-700/50 p-4 bg-gray-800/50">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-300">Use LangChain Enhanced Mode</span>
                <button
                  onClick={toggleLangChain}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    useLangChain ? 'bg-cyan-500' : 'bg-gray-600'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      useLangChain ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
              <div className="text-xs text-gray-400">
                {useLangChain
                  ? '🧠 Enhanced memory, conversation context, and advanced reasoning'
                  : '⚡ Fast rule-based responses with basic AI integration'
                }
              </div>
              {langchainService && useLangChain && (
                <div className="text-xs text-green-400 flex items-center gap-1">
                  <Sparkles className="w-3 h-3" />
                  LangChain service active
                </div>
              )}
            </div>
          </div>
        )}

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              {message.sender === 'bot' && (
                <div className="bg-gradient-to-r from-cyan-500 to-blue-500 p-2 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0">
                  <Bot className="w-4 h-4 text-white" />
                </div>
              )}
              
              <div
                className={`max-w-[80%] p-3 rounded-2xl ${
                  message.sender === 'user'
                    ? 'bg-gradient-to-r from-cyan-500 to-blue-500 text-white'
                    : 'bg-gray-800/80 text-gray-200 border border-gray-700/50'
                }`}
              >
                <p className="text-sm leading-relaxed whitespace-pre-line">{message.text}</p>
                <p className="text-xs opacity-70 mt-1">
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </p>
              </div>

              {message.sender === 'user' && (
                <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-2 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0">
                  <User className="w-4 h-4 text-white" />
                </div>
              )}
            </div>
          ))}

          {isTyping && (
            <div className="flex gap-3 justify-start">
              <div className="bg-gradient-to-r from-cyan-500 to-blue-500 p-2 rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0">
                <Bot className="w-4 h-4 text-white" />
              </div>
              <div className="bg-gray-800/80 border border-gray-700/50 p-3 rounded-2xl">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce animation-delay-200"></div>
                  <div className="w-2 h-2 bg-cyan-400 rounded-full animate-bounce animation-delay-400"></div>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        <div className="p-4 border-t border-gray-700/50">
          <div className="flex gap-2">
            <input
              type="text"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me about course design..."
              className="flex-1 bg-gray-800/80 border border-gray-700/50 rounded-xl px-4 py-2 text-gray-200 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
            />
            <button
              onClick={handleSendMessage}
              disabled={!inputText.trim()}
              className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white p-2 rounded-xl hover:from-cyan-600 hover:to-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105"
            >
              <Send className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};