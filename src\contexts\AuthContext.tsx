import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, AuthResponse, UserCredentials, UserRegistration } from '../services/databaseService';
import databaseService from '../services/databaseService';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: UserCredentials) => Promise<AuthResponse>;
  register: (userData: UserRegistration) => Promise<AuthResponse>;
  logout: () => void;
  updatePreferences: (cosCount: number, unitsCount: number) => Promise<boolean>;
  getUserPreferences: () => Promise<{ cosCount: number; unitsCount: number }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    try {
      // Check for existing token on app load
      const token = localStorage.getItem('auth_token');
      if (token) {
        verifyAndSetUser(token);
      } else {
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Auth initialization error:', error);
      setError('Failed to initialize authentication');
      setIsLoading(false);
    }
  }, []);

  const verifyAndSetUser = (token: string) => {
    try {
      const verification = databaseService.verifyToken(token);

      if (verification.valid && verification.userId) {
        const userData = databaseService.getUserById(verification.userId);

        if (userData) {
          setUser(userData);
        } else {
          localStorage.removeItem('auth_token');
        }
      } else {
        localStorage.removeItem('auth_token');
      }
    } catch (error) {
      console.error('Token verification failed:', error);
      setError('Token verification failed');
      localStorage.removeItem('auth_token');
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (credentials: UserCredentials): Promise<AuthResponse> => {
    try {
      const response = databaseService.loginUser(credentials);
      if (response.success && response.user && response.token) {
        setUser(response.user);
        localStorage.setItem('auth_token', response.token);
      }
      return response;
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, message: 'Login failed due to an error' };
    }
  };

  const register = async (userData: UserRegistration): Promise<AuthResponse> => {
    try {
      const response = databaseService.registerUser(userData);
      return response;
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, message: 'Registration failed due to an error' };
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('auth_token');
  };

  const updatePreferences = async (cosCount: number, unitsCount: number): Promise<boolean> => {
    if (!user) return false;
    try {
      return databaseService.updateUserPreferences(user.id, cosCount, unitsCount);
    } catch (error) {
      console.error('Error updating preferences:', error);
      return false;
    }
  };

  const getUserPreferences = async (): Promise<{ cosCount: number; unitsCount: number }> => {
    if (!user) return { cosCount: 5, unitsCount: 5 };
    try {
      return databaseService.getUserPreferences(user.id);
    } catch (error) {
      console.error('Error fetching preferences:', error);
      return { cosCount: 5, unitsCount: 5 };
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
    updatePreferences,
    getUserPreferences
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
