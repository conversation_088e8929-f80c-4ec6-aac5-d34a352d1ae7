{"name": "course-design-system", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@langchain/anthropic": "^0.3.24", "@langchain/community": "^0.3.48", "@langchain/core": "^0.3.62", "@langchain/google-genai": "^0.2.14", "@langchain/openai": "^0.5.18", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "cheerio": "^1.1.0", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.1", "jspdf-autotable": "^5.0.2", "langchain": "^0.3.29", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "sqlite3": "^5.1.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}